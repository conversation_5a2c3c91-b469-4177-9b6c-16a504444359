from DrissionPage import Chromium
import json
import time
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime

class MultiStoreManager:
    def __init__(self):
        # 初始化GUI界面
        self.root = tk.Tk()
        self.root.title("多店铺选取切换管理器")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 设置全局微软雅黑字体
        self.font_style = ("Microsoft YaHei", 10)
        self.root.option_add("*Font", self.font_style)
        
        # 浏览器和标签页对象
        self.browser = None
        self.tab = None
        self.is_monitoring = False
        self.store_list = []  # 存储店铺信息
        self.current_store_id = None  # 当前选中的店铺ID
        
        # 创建GUI界面
        self.create_gui()
        
        print("🚀 多店铺选取切换管理器已启动")
        print("📋 功能说明：")
        print("   1. 监控用户信息API获取店铺列表")
        print("   2. 监控菜单权限API获取认证信息")
        print("   3. 提供GUI界面进行店铺切换")
        
    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = tk.Label(main_frame, text="多店铺选取切换管理器", 
                              font=("Microsoft YaHei", 16, "bold"), 
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 控制按钮区域
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 开始监控按钮
        self.start_btn = tk.Button(control_frame, text="开始监控", 
                                  command=self.start_monitoring,
                                  bg='#27ae60', fg='white', 
                                  font=("Microsoft YaHei", 10, "bold"),
                                  width=12, height=2)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 停止监控按钮
        self.stop_btn = tk.Button(control_frame, text="停止监控", 
                                 command=self.stop_monitoring,
                                 bg='#e74c3c', fg='white',
                                 font=("Microsoft YaHei", 10, "bold"),
                                 width=12, height=2, state='disabled')
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 状态显示
        self.status_label = tk.Label(control_frame, text="状态: 未开始", 
                                    font=("Microsoft YaHei", 10),
                                    bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.grid(row=0, column=2, padx=(20, 0))
        
        # 店铺选择区域
        store_frame = ttk.LabelFrame(main_frame, text="店铺选择", padding="10")
        store_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 店铺列表
        tk.Label(store_frame, text="可用店铺:", font=("Microsoft YaHei", 10)).grid(row=0, column=0, sticky=tk.W)
        
        self.store_listbox = tk.Listbox(store_frame, height=6, font=("Microsoft YaHei", 9))
        self.store_listbox.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 10))
        self.store_listbox.bind('<<ListboxSelect>>', self.on_store_select)
        
        # 选择按钮
        self.select_btn = tk.Button(store_frame, text="选择此店铺", 
                                   command=self.select_store,
                                   bg='#3498db', fg='white',
                                   font=("Microsoft YaHei", 10),
                                   width=15, state='disabled')
        self.select_btn.grid(row=2, column=0, pady=(0, 5))
        
        # 当前店铺显示
        self.current_store_label = tk.Label(store_frame, text="当前店铺: 未选择", 
                                           font=("Microsoft YaHei", 10, "bold"),
                                           fg='#2c3e50')
        self.current_store_label.grid(row=2, column=1, padx=(20, 0))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 font=("Microsoft YaHei", 9),
                                                 wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_btn = tk.Button(log_frame, text="清空日志", 
                             command=self.clear_log,
                             bg='#95a5a6', fg='white',
                             font=("Microsoft YaHei", 9))
        clear_btn.grid(row=1, column=0, pady=(5, 0))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        store_frame.columnconfigure(0, weight=1)
        
    def log_message(self, message):
        """在GUI中显示日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 在GUI中显示
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        
        # 同时在控制台输出
        print(formatted_message.strip())
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        print("📝 日志已清空")
        
    def update_status(self, status):
        """更新状态显示"""
        self.status_label.config(text=f"状态: {status}")
        
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.log_message("⚠️ 监控已在运行中")
            return
            
        self.log_message("🚀 开始启动多店铺监控系统...")
        self.update_status("正在启动...")
        
        # 在新线程中运行监控
        monitor_thread = threading.Thread(target=self.run_monitoring, daemon=True)
        monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.update_status("正在停止...")
        self.log_message("🛑 正在停止监控...")
        
        if self.tab:
            try:
                self.tab.listen.stop()
                self.log_message("✅ API监听已停止")
            except Exception as e:
                self.log_message(f"⚠️ 停止监听时出现异常: {str(e)}")
        
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.update_status("已停止")
        
    def run_monitoring(self):
        """运行监控的主要逻辑"""
        try:
            self.is_monitoring = True
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            
            # 初始化浏览器
            self.log_message("🌐 正在连接到浏览器 (端口: 9222)...")
            self.browser = Chromium(9222)
            self.tab = self.browser.new_tab()
            self.tab.set.activate()
            self.log_message("✅ 浏览器连接成功，新标签页已创建")
            
            # 设置监听目标API
            userinfo_api = "api/seller/auth/userInfo"
            menu_api = "api/seller/auth/menu"
            
            self.log_message(f"🎯 开始监听用户信息API: {userinfo_api}")
            self.log_message(f"🎯 开始监听菜单权限API: {menu_api}")
            
            # 同时监听两个API
            self.tab.listen.start(targets=[userinfo_api, menu_api])
            
            # 访问目标页面
            target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
            self.log_message(f"🔗 正在访问目标页面: {target_url}")
            self.update_status("正在加载页面...")
            
            self.tab.get(target_url)
            self.log_message("✅ 页面加载完成，开始监控API请求...")
            self.update_status("监控中...")
            
            # 持续监控API请求
            while self.is_monitoring:
                try:
                    packet = self.tab.listen.wait(timeout=2)
                    if packet:
                        self.process_api_packet(packet)
                except Exception as e:
                    if self.is_monitoring:  # 只有在仍在监控时才记录错误
                        self.log_message(f"⚠️ 监控过程中出现异常: {str(e)}")
                    
        except Exception as e:
            self.log_message(f"❌ 监控启动失败: {str(e)}")
            self.update_status("启动失败")
        finally:
            if self.is_monitoring:
                self.stop_monitoring()
                
    def process_api_packet(self, packet):
        """处理捕获到的API数据包"""
        api_url = packet.url
        
        if "api/seller/auth/userInfo" in api_url:
            self.log_message("🔍 捕获到用户信息API请求")
            self.process_userinfo_response(packet)
        elif "api/seller/auth/menu" in api_url:
            self.log_message("🔍 捕获到菜单权限API请求")
            self.process_menu_response(packet)
            
    def process_userinfo_response(self, packet):
        """处理用户信息API响应"""
        try:
            if packet.response and packet.response.body:
                response_data = json.loads(packet.response.body)
                self.log_message("📊 用户信息API响应解析成功")
                
                if response_data.get('success') and response_data.get('result'):
                    result = response_data['result']
                    account_id = result.get('accountId')
                    mall_list = result.get('mallList', [])
                    
                    self.log_message(f"👤 账户ID: {account_id}")
                    self.log_message(f"🏪 发现 {len(mall_list)} 个店铺")
                    
                    # 更新店铺列表
                    self.store_list = mall_list
                    self.update_store_list()
                    
                    # 显示店铺详细信息
                    for i, mall in enumerate(mall_list, 1):
                        mall_id = mall.get('mallId')
                        mall_name = mall.get('mallName')
                        managed_type = mall.get('managedType')
                        self.log_message(f"   {i}. 店铺名称: {mall_name}")
                        self.log_message(f"      店铺ID: {mall_id}")
                        self.log_message(f"      管理类型: {managed_type}")
                        
                else:
                    self.log_message("⚠️ 用户信息API响应格式异常")
                    
        except json.JSONDecodeError:
            self.log_message("❌ 用户信息API响应JSON解析失败")
        except Exception as e:
            self.log_message(f"❌ 处理用户信息API响应时出错: {str(e)}")
            
    def process_menu_response(self, packet):
        """处理菜单权限API响应"""
        try:
            self.log_message("🔐 正在提取菜单权限API的认证信息...")
            
            # 提取重要的请求头信息
            auth_headers = self.extract_auth_headers(packet)
            
            if auth_headers:
                self.log_message("✅ 认证信息提取成功")
                self.log_message(f"🔑 提取到 {len(auth_headers)} 个重要请求头")
                
                # 显示关键认证信息（不显示完整内容，只显示存在性）
                key_headers = ['Anti-Content', 'mallid', 'authorization', 'x-csrf-token']
                for header in key_headers:
                    if any(h.lower() == header.lower() for h in auth_headers.keys()):
                        self.log_message(f"   ✓ {header}: 已获取")
                    else:
                        self.log_message(f"   ✗ {header}: 未找到")
                        
            else:
                self.log_message("⚠️ 未能提取到有效的认证信息")
                
        except Exception as e:
            self.log_message(f"❌ 处理菜单权限API响应时出错: {str(e)}")

    def extract_auth_headers(self, packet):
        """从API请求中提取认证头信息"""
        if not packet or not packet.request:
            self.log_message("⚠️ 无效的数据包或请求信息")
            return None

        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'user-agent',
            'accept', 'accept-language', 'content-type',
            'origin', 'referer'
        ]

        self.log_message("🔍 开始提取重要的请求头信息...")

        if packet.request.headers:
            for key, value in packet.request.headers.items():
                if any(important_header.lower() == key.lower() for important_header in important_headers):
                    clean_key = key.strip()
                    clean_value = str(value).strip()
                    if clean_key and clean_value:
                        auth_headers[clean_key] = clean_value

        self.log_message(f"📋 成功提取 {len(auth_headers)} 个认证头信息")
        return auth_headers

    def update_store_list(self):
        """更新GUI中的店铺列表"""
        self.log_message("🔄 正在更新GUI店铺列表...")

        # 清空现有列表
        self.store_listbox.delete(0, tk.END)

        # 添加店铺到列表
        for mall in self.store_list:
            mall_name = mall.get('mallName', '未知店铺')
            mall_id = mall.get('mallId', '未知ID')
            display_text = f"{mall_name} (ID: {mall_id})"
            self.store_listbox.insert(tk.END, display_text)

        self.log_message(f"✅ 店铺列表更新完成，共 {len(self.store_list)} 个店铺")

        # 启用选择按钮
        if self.store_list:
            self.select_btn.config(state='normal')

    def on_store_select(self, event):
        """当用户在列表中选择店铺时触发"""
        selection = self.store_listbox.curselection()
        if selection:
            index = selection[0]
            selected_store = self.store_list[index]
            store_name = selected_store.get('mallName', '未知店铺')
            store_id = selected_store.get('mallId', '未知ID')

            self.log_message(f"👆 用户选择了店铺: {store_name} (ID: {store_id})")

    def select_store(self):
        """选择当前店铺"""
        selection = self.store_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个店铺")
            return

        index = selection[0]
        selected_store = self.store_list[index]
        store_name = selected_store.get('mallName', '未知店铺')
        store_id = selected_store.get('mallId', '未知ID')

        self.current_store_id = store_id
        self.current_store_label.config(text=f"当前店铺: {store_name}")

        self.log_message(f"✅ 已选择店铺: {store_name}")
        self.log_message(f"🏪 当前店铺ID: {store_id}")

        messagebox.showinfo("成功", f"已选择店铺: {store_name}\n店铺ID: {store_id}")

    def run(self):
        """运行GUI应用"""
        self.log_message("🎨 GUI界面已启动")
        self.log_message("📖 使用说明:")
        self.log_message("   1. 点击'开始监控'按钮启动API监控")
        self.log_message("   2. 系统会自动获取店铺列表")
        self.log_message("   3. 在店铺列表中选择要切换的店铺")
        self.log_message("   4. 点击'选择此店铺'确认切换")
        self.log_message("=" * 50)

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("👋 程序被用户中断")
        except Exception as e:
            self.log_message(f"❌ GUI运行时出现异常: {str(e)}")
        finally:
            if self.is_monitoring:
                self.stop_monitoring()
            self.log_message("👋 多店铺选取切换管理器已关闭")

if __name__ == "__main__":
    try:
        print("=" * 60)
        print("🚀 多店铺选取切换管理器")
        print("=" * 60)
        print("📋 功能特点:")
        print("   • 监控用户信息API获取店铺列表")
        print("   • 监控菜单权限API获取认证信息")
        print("   • 提供友好的GUI界面")
        print("   • 支持多店铺选择和切换")
        print("   • 详细的调试输出信息")
        print("=" * 60)

        # 创建并运行应用
        app = MultiStoreManager()
        app.run()

    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
