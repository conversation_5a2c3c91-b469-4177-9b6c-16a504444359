from DrissionPage import Chromium
import json
import time
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime

class MultiStoreManager:
    def __init__(self):
        # 初始化GUI界面
        self.root = tk.Tk()
        self.root.title("多店铺选取切换管理器")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 设置全局微软雅黑字体
        self.font_style = ("Microsoft YaHei", 10)
        self.root.option_add("*Font", self.font_style)
        
        # 浏览器和标签页对象
        self.browser = None
        self.tab = None
        self.is_monitoring = False
        self.store_list = []  # 存储店铺信息
        self.current_store_id = None  # 当前选中的店铺ID
        self.auth_headers = None  # 存储认证头信息
        
        # 创建GUI界面
        self.create_gui()
        
        print("🚀 多店铺选取切换管理器已启动")
        print("📋 功能说明：")
        print("   1. 监控用户信息API获取店铺列表")
        print("   2. 监控菜单权限API获取认证信息")
        print("   3. 提供GUI界面进行店铺切换")
        
    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = tk.Label(main_frame, text="多店铺选取切换管理器",
                              font=("Microsoft YaHei", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 左侧控制区域
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 控制按钮区域
        control_frame = ttk.LabelFrame(left_frame, text="控制面板", padding="15")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 统一按钮大小
        button_width = 15
        button_height = 2

        # 刷新店铺按钮
        self.refresh_btn = tk.Button(control_frame, text="刷新店铺",
                                    command=self.start_monitoring,
                                    font=("Microsoft YaHei", 10),
                                    width=button_width, height=button_height)
        self.refresh_btn.grid(row=0, column=0, pady=(0, 10))

        # 拉取订单按钮
        self.fetch_orders_btn = tk.Button(control_frame, text="拉取店铺订单",
                                         command=self.fetch_store_orders,
                                         font=("Microsoft YaHei", 10),
                                         width=button_width, height=button_height,
                                         state='disabled')
        self.fetch_orders_btn.grid(row=1, column=0, pady=(0, 10))

        # 停止监控按钮
        self.stop_btn = tk.Button(control_frame, text="停止监控",
                                 command=self.stop_monitoring,
                                 font=("Microsoft YaHei", 10),
                                 width=button_width, height=button_height,
                                 state='disabled')
        self.stop_btn.grid(row=2, column=0, pady=(0, 10))

        # 状态显示
        self.status_label = tk.Label(control_frame, text="状态: 未开始",
                                    font=("Microsoft YaHei", 10))
        self.status_label.grid(row=3, column=0, pady=(10, 0))
        
        # 店铺选择区域
        store_frame = ttk.LabelFrame(left_frame, text="店铺选择", padding="15")
        store_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 店铺下拉框标签
        tk.Label(store_frame, text="选择店铺:",
                font=("Microsoft YaHei", 10)).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # 店铺下拉框
        self.store_var = tk.StringVar()
        self.store_combobox = ttk.Combobox(store_frame, textvariable=self.store_var,
                                          font=("Microsoft YaHei", 9),
                                          state="readonly", width=25)
        self.store_combobox.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.store_combobox.bind('<<ComboboxSelected>>', self.on_store_select)

        # 当前店铺信息显示
        current_store_frame = ttk.LabelFrame(left_frame, text="当前选择", padding="15")
        current_store_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        self.current_store_label = tk.Label(current_store_frame, text="当前店铺: 未选择",
                                           font=("Microsoft YaHei", 10),
                                           wraplength=200)
        self.current_store_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.current_store_id_label = tk.Label(current_store_frame, text="店铺ID: 未选择",
                                              font=("Microsoft YaHei", 9))
        self.current_store_id_label.grid(row=1, column=0, sticky=tk.W)
        
        # 右侧日志区域
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 日志输出区域
        log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding="15")
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=60,
                                                 font=("Microsoft YaHei", 9),
                                                 wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 清空日志按钮
        clear_btn = tk.Button(log_frame, text="清空日志",
                             command=self.clear_log,
                             font=("Microsoft YaHei", 9),
                             width=button_width, height=1)
        clear_btn.grid(row=1, column=0, sticky=tk.W)

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        store_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(0, weight=1)
        current_store_frame.columnconfigure(0, weight=1)
        
    def log_message(self, message, gui_only=False, console_only=False):
        """在GUI和控制台显示日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # 在GUI中显示（除非指定仅控制台）
        if not console_only:
            self.log_text.insert(tk.END, formatted_message + "\n")
            self.log_text.see(tk.END)

        # 在控制台输出（除非指定仅GUI）
        if not gui_only:
            print(formatted_message)

    def log_console_detail(self, message):
        """仅在控制台显示详细信息"""
        self.log_message(message, console_only=True)
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        print("📝 日志已清空")
        
    def update_status(self, status):
        """更新状态显示"""
        self.status_label.config(text=f"状态: {status}")
        
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.log_message("⚠️ 监控已在运行中")
            return
            
        self.log_message("🚀 开始启动多店铺监控系统...")
        self.update_status("正在启动...")
        
        # 在新线程中运行监控
        monitor_thread = threading.Thread(target=self.run_monitoring, daemon=True)
        monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.update_status("正在停止...")
        self.log_message("🛑 正在停止监控...")
        
        if self.tab:
            try:
                self.tab.listen.stop()
                self.log_message("✅ API监听已停止")
            except Exception as e:
                self.log_message(f"⚠️ 停止监听时出现异常: {str(e)}")
        
        self.refresh_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.update_status("已停止")
        
    def run_monitoring(self):
        """运行监控的主要逻辑"""
        try:
            self.is_monitoring = True
            self.refresh_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            
            # 初始化浏览器
            self.log_message("🌐 正在连接到浏览器 (端口: 9222)...")
            self.browser = Chromium(9222)
            self.tab = self.browser.new_tab()
            self.tab.set.activate()
            self.log_message("✅ 浏览器连接成功，新标签页已创建")
            
            # 设置监听目标API
            userinfo_api = "api/seller/auth/userInfo"
            menu_api = "api/seller/auth/menu"
            
            self.log_message(f"🎯 开始监听用户信息API: {userinfo_api}")
            self.log_message(f"🎯 开始监听菜单权限API: {menu_api}")
            
            # 同时监听两个API
            self.tab.listen.start(targets=[userinfo_api, menu_api])
            
            # 访问目标页面
            target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
            self.log_message(f"🔗 正在访问目标页面: {target_url}")
            self.update_status("正在加载页面...")
            
            self.tab.get(target_url)
            self.log_message("✅ 页面加载完成，开始监控API请求...")
            self.update_status("监控中...")
            
            # 持续监控API请求
            while self.is_monitoring:
                try:
                    packet = self.tab.listen.wait(timeout=2)
                    if packet:
                        self.process_api_packet(packet)
                except Exception as e:
                    if self.is_monitoring:  # 只有在仍在监控时才记录错误
                        self.log_message(f"⚠️ 监控过程中出现异常: {str(e)}")
                    
        except Exception as e:
            self.log_message(f"❌ 监控启动失败: {str(e)}")
            self.update_status("启动失败")
        finally:
            if self.is_monitoring:
                self.stop_monitoring()
                
    def process_api_packet(self, packet):
        """处理捕获到的API数据包"""
        api_url = packet.url
        
        if "api/seller/auth/userInfo" in api_url:
            self.log_message("🔍 捕获到用户信息API请求")
            self.process_userinfo_response(packet)
        elif "api/seller/auth/menu" in api_url:
            self.log_message("🔍 捕获到菜单权限API请求")
            self.process_menu_response(packet)
            
    def process_userinfo_response(self, packet):
        """处理用户信息API响应"""
        try:
            if packet.response and packet.response.body:
                # 处理响应数据，可能是字符串或字典格式
                if isinstance(packet.response.body, str):
                    response_data = json.loads(packet.response.body)
                else:
                    response_data = packet.response.body

                # 控制台详细输出
                self.log_console_detail("=" * 80)
                self.log_console_detail("📊 用户信息API详细响应:")
                self.log_console_detail(f"完整响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                self.log_console_detail("=" * 80)

                # GUI简化显示
                self.log_message("📊 用户信息API响应解析成功", gui_only=True)

                if response_data.get('success') and response_data.get('result'):
                    result = response_data['result']
                    account_id = result.get('accountId')
                    mall_list = result.get('mallList', [])

                    # GUI显示关键信息
                    self.log_message(f"👤 账户ID: {account_id}", gui_only=True)
                    self.log_message(f"🏪 发现 {len(mall_list)} 个店铺", gui_only=True)

                    # 控制台显示详细店铺信息
                    self.log_console_detail(f"账户详细信息:")
                    self.log_console_detail(f"  账户ID: {account_id}")
                    self.log_console_detail(f"  账户类型: {result.get('accountType')}")
                    self.log_console_detail(f"  手机号掩码: {result.get('maskMobile')}")
                    self.log_console_detail(f"店铺列表详情:")

                    # 更新店铺列表
                    self.store_list = mall_list
                    self.update_store_list()

                    # 显示店铺详细信息
                    for i, mall in enumerate(mall_list, 1):
                        mall_id = mall.get('mallId')
                        mall_name = mall.get('mallName')
                        managed_type = mall.get('managedType')

                        # GUI简化显示
                        self.log_message(f"   {i}. {mall_name} (ID: {mall_id})", gui_only=True)

                        # 控制台详细显示
                        self.log_console_detail(f"  店铺 {i}:")
                        self.log_console_detail(f"    名称: {mall_name}")
                        self.log_console_detail(f"    ID: {mall_id}")
                        self.log_console_detail(f"    管理类型: {managed_type}")

                else:
                    self.log_message("⚠️ 用户信息API响应格式异常")
                    self.log_console_detail(f"响应格式异常，完整数据: {response_data}")

        except json.JSONDecodeError as e:
            self.log_message("❌ 用户信息API响应JSON解析失败")
            self.log_console_detail(f"JSON解析失败详情: {str(e)}")
            self.log_console_detail(f"原始响应: {packet.response.body}")
        except Exception as e:
            self.log_message(f"❌ 处理用户信息API响应时出错: {str(e)}")
            self.log_console_detail(f"处理异常详情: {str(e)}")
            self.log_console_detail(f"异常类型: {type(e).__name__}")
            
    def process_menu_response(self, packet):
        """处理菜单权限API响应"""
        try:
            # GUI简化显示
            self.log_message("🔐 正在提取菜单权限API的认证信息...", gui_only=True)

            # 控制台详细输出
            self.log_console_detail("=" * 80)
            self.log_console_detail("🔐 菜单权限API详细请求信息:")
            self.log_console_detail(f"请求URL: {packet.url}")
            self.log_console_detail(f"请求方法: {packet.method}")

            # 提取重要的请求头信息
            auth_headers = self.extract_auth_headers(packet)

            if auth_headers:
                # 保存认证信息供后续使用
                self.auth_headers = auth_headers

                # GUI简化显示
                self.log_message("✅ 认证信息提取成功", gui_only=True)
                self.log_message(f"🔑 提取到 {len(auth_headers)} 个重要请求头", gui_only=True)

                # 控制台详细显示所有请求头
                self.log_console_detail("完整请求头信息:")
                for key, value in auth_headers.items():
                    if key.lower() == 'cookie':
                        # Cookie太长，只显示前100个字符
                        display_value = value[:100] + "..." if len(value) > 100 else value
                        self.log_console_detail(f"  {key}: {display_value}")
                    else:
                        self.log_console_detail(f"  {key}: {value}")

                # GUI显示关键认证信息（不显示完整内容，只显示存在性）
                key_headers = ['Anti-Content', 'mallid', 'cookie', 'authorization', 'x-csrf-token']
                for header in key_headers:
                    if any(h.lower() == header.lower() for h in auth_headers.keys()):
                        self.log_message(f"   ✓ {header}: 已获取", gui_only=True)
                    else:
                        self.log_message(f"   ✗ {header}: 未找到", gui_only=True)

                # 显示cookie中的mallid信息
                cookie_header = None
                for key, value in auth_headers.items():
                    if key.lower() == 'cookie':
                        cookie_header = value
                        break

                if cookie_header and 'mallid=' in cookie_header:
                    # 提取cookie中的mallid
                    import re
                    mallid_match = re.search(r'mallid=([^;]+)', cookie_header)
                    if mallid_match:
                        current_mallid = mallid_match.group(1)
                        self.log_message(f"🍪 Cookie中的mallid: {current_mallid}", gui_only=True)
                        self.log_console_detail(f"Cookie中提取的mallid: {current_mallid}")

                self.log_console_detail("=" * 80)

            else:
                self.log_message("⚠️ 未能提取到有效的认证信息")
                self.log_console_detail("认证信息提取失败")

        except Exception as e:
            self.log_message(f"❌ 处理菜单权限API响应时出错: {str(e)}")
            self.log_console_detail(f"处理菜单权限API异常详情: {str(e)}")
            self.log_console_detail(f"异常类型: {type(e).__name__}")

    def extract_auth_headers(self, packet):
        """从API请求中提取认证头信息"""
        if not packet or not packet.request:
            self.log_message("⚠️ 无效的数据包或请求信息")
            return None

        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'user-agent',
            'accept', 'accept-language', 'content-type',
            'origin', 'referer'
        ]

        self.log_message("🔍 开始提取重要的请求头信息...")

        if packet.request.headers:
            for key, value in packet.request.headers.items():
                if any(important_header.lower() == key.lower() for important_header in important_headers):
                    clean_key = key.strip()
                    clean_value = str(value).strip()
                    if clean_key and clean_value:
                        auth_headers[clean_key] = clean_value

        self.log_message(f"📋 成功提取 {len(auth_headers)} 个认证头信息")
        return auth_headers

    def update_store_list(self):
        """更新GUI中的店铺下拉框"""
        self.log_message("🔄 正在更新GUI店铺下拉框...")

        # 清空现有下拉框
        self.store_combobox['values'] = ()
        self.store_var.set('')

        # 添加店铺到下拉框
        store_options = []
        for mall in self.store_list:
            mall_name = mall.get('mallName', '未知店铺')
            mall_id = mall.get('mallId', '未知ID')
            display_text = f"{mall_name} (ID: {mall_id})"
            store_options.append(display_text)

        self.store_combobox['values'] = store_options

        self.log_message(f"✅ 店铺下拉框更新完成，共 {len(self.store_list)} 个店铺")

        # 启用拉取订单按钮
        if self.store_list:
            self.fetch_orders_btn.config(state='normal')
            # 默认选择第一个店铺
            if store_options:
                self.store_combobox.current(0)
                self.on_store_select(None)

    def on_store_select(self, event):
        """当用户在下拉框中选择店铺时触发"""
        # 忽略event参数，仅用于绑定事件
        _ = event

        current_selection = self.store_combobox.current()
        if current_selection >= 0 and current_selection < len(self.store_list):
            selected_store = self.store_list[current_selection]
            store_name = selected_store.get('mallName', '未知店铺')
            store_id = selected_store.get('mallId', '未知ID')

            # 更新当前选择的店铺
            self.current_store_id = store_id
            self.current_store_label.config(text=f"当前店铺: {store_name}")
            self.current_store_id_label.config(text=f"店铺ID: {store_id}")

            self.log_message(f"🏪 已切换到店铺: {store_name} (ID: {store_id})")
            self.log_message(f"📋 mallid将设置为: {store_id}")

    def fetch_store_orders(self):
        """拉取当前选择店铺的订单"""
        if not self.current_store_id:
            self.log_message("⚠️ 请先选择一个店铺")
            return

        if not hasattr(self, 'auth_headers') or not self.auth_headers:
            self.log_message("⚠️ 请先刷新店铺获取认证信息")
            return

        self.log_message(f"🚀 开始拉取店铺订单 (店铺ID: {self.current_store_id})...")

        # 在新线程中执行订单拉取
        fetch_thread = threading.Thread(target=self.fetch_orders_thread, daemon=True)
        fetch_thread.start()

    def fetch_orders_thread(self):
        """在后台线程中拉取订单数据"""
        try:
            # 修改认证头中的mallid
            modified_headers = self.auth_headers.copy()
            modified_headers['mallid'] = str(self.current_store_id)

            # GUI简化显示
            self.log_message(f"🔧 修改请求头mallid为: {self.current_store_id}", gui_only=True)

            # 控制台详细显示请求头信息
            self.log_console_detail("=" * 80)
            self.log_console_detail("🔧 订单请求详细信息:")
            self.log_console_detail(f"目标店铺ID: {self.current_store_id}")
            self.log_console_detail("修改后的完整请求头:")
            for key, value in modified_headers.items():
                self.log_console_detail(f"  {key}: {value}")

            # 构建订单查询请求数据
            custom_request_data = {
                "pageNo": 1,
                "pageSize": 100,
                "urgencyType": 1,
                "isCustomGoods": False,
                "statusList": [1],
                "oneDimensionSort": {
                    "firstOrderByParam": "expectLatestDeliverTime",
                    "firstOrderByDesc": 0
                }
            }

            api_url = "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList"

            # GUI简化显示
            self.log_message(f"🌐 发送订单查询请求...", gui_only=True)

            # 控制台详细显示
            self.log_console_detail(f"请求URL: {api_url}")
            self.log_console_detail(f"请求方法: POST")
            self.log_console_detail(f"请求数据: {json.dumps(custom_request_data, ensure_ascii=False, indent=2)}")
            self.log_console_detail("=" * 80)

            # 发送API请求
            response_data = self.send_custom_api_request(self.tab, modified_headers, custom_request_data, api_url)

            if response_data:
                self.log_message("✅ 订单数据拉取成功", gui_only=True)
                self.process_order_response(response_data)
            else:
                self.log_message("❌ 订单数据拉取失败")
                self.log_console_detail("订单数据拉取失败，无响应数据")

        except Exception as e:
            self.log_message(f"❌ 拉取订单时出现异常: {str(e)}")
            self.log_console_detail(f"拉取订单异常详情: {str(e)}")
            import traceback
            self.log_console_detail(f"异常堆栈: {traceback.format_exc()}")

    def process_order_response(self, response_data):
        """处理订单响应数据"""
        try:
            # GUI简化显示
            self.log_message("📊 开始解析订单响应数据...", gui_only=True)

            # 控制台详细输出完整响应
            self.log_console_detail("=" * 100)
            self.log_console_detail("📊 订单API完整响应数据:")
            self.log_console_detail(f"完整响应对象: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            self.log_console_detail("=" * 100)

            if response_data.get('success', True) and 'body' in response_data:
                try:
                    if isinstance(response_data['body'], str):
                        order_data = json.loads(response_data['body'])
                    else:
                        order_data = response_data['body']

                    # 控制台显示解析后的订单数据
                    self.log_console_detail("解析后的订单数据:")
                    self.log_console_detail(f"{json.dumps(order_data, ensure_ascii=False, indent=2)}")

                    # GUI显示基本状态信息
                    self.log_message(f"📈 响应状态: {response_data.get('status', 'Unknown')}", gui_only=True)
                    status_text = response_data.get('statusText', 'Unknown')
                    if status_text:
                        self.log_message(f"📝 响应状态文本: {status_text}", gui_only=True)

                    # 控制台显示详细状态信息
                    self.log_console_detail(f"HTTP状态码: {response_data.get('status')}")
                    self.log_console_detail(f"状态文本: {response_data.get('statusText')}")
                    self.log_console_detail(f"响应头: {response_data.get('headers', {})}")

                    if order_data.get('success'):
                        result = order_data.get('result', {})
                        total_count = result.get('totalCount', 0)
                        order_list = result.get('list', [])

                        # GUI显示订单统计
                        self.log_message(f"📦 订单总数: {total_count}", gui_only=True)
                        self.log_message(f"📋 当前页订单数: {len(order_list)}", gui_only=True)

                        # 控制台显示详细订单统计
                        self.log_console_detail(f"订单统计信息:")
                        self.log_console_detail(f"  总订单数: {total_count}")
                        self.log_console_detail(f"  当前页订单数: {len(order_list)}")
                        self.log_console_detail(f"  分页信息: 第{result.get('pageNo', 1)}页，每页{result.get('pageSize', 0)}条")

                        # 显示订单详细信息
                        if order_list:
                            self.log_console_detail("订单详细信息:")
                            for i, order in enumerate(order_list, 1):
                                order_id = order.get('subOrderId', '未知')
                                order_status = order.get('statusDesc', '未知状态')
                                product_name = order.get('productName', '未知商品')

                                # GUI显示前3个订单的简要信息
                                if i <= 3:
                                    self.log_message(f"   {i}. {product_name[:20]}... (ID: {order_id})", gui_only=True)

                                # 控制台显示所有订单的详细信息
                                self.log_console_detail(f"  订单 {i}:")
                                self.log_console_detail(f"    订单ID: {order_id}")
                                self.log_console_detail(f"    状态: {order_status}")
                                self.log_console_detail(f"    商品名称: {product_name}")
                                self.log_console_detail(f"    商品ID: {order.get('productId', '未知')}")
                                self.log_console_detail(f"    SKU ID: {order.get('skuId', '未知')}")
                                self.log_console_detail(f"    数量: {order.get('quantity', '未知')}")
                                self.log_console_detail(f"    单价: {order.get('unitPrice', '未知')}")
                                self.log_console_detail(f"    总价: {order.get('totalPrice', '未知')}")
                                self.log_console_detail(f"    创建时间: {order.get('createTime', '未知')}")
                                self.log_console_detail(f"    期望发货时间: {order.get('expectLatestDeliverTime', '未知')}")
                                self.log_console_detail(f"    完整订单数据: {json.dumps(order, ensure_ascii=False, indent=4)}")
                                self.log_console_detail("-" * 50)

                            if len(order_list) > 3:
                                self.log_message(f"   ... 还有 {len(order_list) - 3} 个订单", gui_only=True)
                        else:
                            self.log_message("📋 当前店铺暂无订单", gui_only=True)
                            self.log_console_detail("当前店铺暂无订单数据")

                    else:
                        error_code = order_data.get('errorCode', '未知')
                        error_msg = order_data.get('errorMsg', '未知错误')
                        self.log_message(f"❌ API返回错误: {error_msg}", gui_only=True)
                        self.log_console_detail(f"API错误详情:")
                        self.log_console_detail(f"  错误代码: {error_code}")
                        self.log_console_detail(f"  错误消息: {error_msg}")
                        self.log_console_detail(f"  完整错误响应: {json.dumps(order_data, ensure_ascii=False, indent=2)}")

                except json.JSONDecodeError as e:
                    self.log_message(f"❌ JSON解析失败: {str(e)}", gui_only=True)
                    self.log_console_detail(f"JSON解析失败详情: {str(e)}")
                    self.log_console_detail(f"原始响应内容: {response_data['body']}")

            else:
                self.log_message("❌ 响应数据格式异常", gui_only=True)
                self.log_console_detail("响应数据格式异常，完整响应:")
                self.log_console_detail(f"{json.dumps(response_data, ensure_ascii=False, indent=2)}")

        except Exception as e:
            self.log_message(f"❌ 处理订单响应时出现异常: {str(e)}")
            self.log_console_detail(f"处理订单响应异常详情: {str(e)}")
            self.log_console_detail(f"异常类型: {type(e).__name__}")
            import traceback
            self.log_console_detail(f"异常堆栈: {traceback.format_exc()}")

    def send_custom_api_request(self, tab, auth_headers, request_data, api_url):
        """发送自定义API请求"""
        if not auth_headers:
            self.log_message("❌ 缺少认证头信息")
            self.log_console_detail("发送API请求失败：缺少认证头信息")
            return None

        try:
            # GUI简化显示
            self.log_message("🚀 开始发送API请求...", gui_only=True)

            # 控制台详细输出
            self.log_console_detail("🚀 开始发送API请求...")
            self.log_console_detail(f"目标URL: {api_url}")

            # 检查标签页是否有效
            try:
                current_url = tab.url
                self.log_console_detail(f"当前标签页URL: {current_url}")
            except Exception as e:
                self.log_message("⚠️ 标签页连接异常，尝试继续...", gui_only=True)
                self.log_console_detail(f"标签页连接异常: {str(e)}")

            # 构建JavaScript代码发送请求
            js_code = f'''
            (async function() {{
                try {{
                    console.log("=== 开始发送API请求 ===");
                    console.log("URL:", '{api_url}');
                    console.log("Headers:", {json.dumps(auth_headers)});
                    console.log("Data:", {json.dumps(request_data)});

                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(auth_headers)},
                        body: JSON.stringify({json.dumps(request_data)})
                    }});

                    console.log("响应状态:", response.status);
                    console.log("响应状态文本:", response.statusText);
                    console.log("响应头:", Object.fromEntries(response.headers.entries()));

                    const responseText = await response.text();
                    console.log("响应内容长度:", responseText.length);
                    console.log("响应内容:", responseText);

                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries()),
                        body: responseText,
                        success: true
                    }};

                    window.customOrderApiResponse = result;
                    console.log("=== 请求完成 ===");
                    return result;
                }} catch (error) {{
                    console.error("=== 请求失败 ===");
                    console.error("错误详情:", error);
                    console.error("错误堆栈:", error.stack);
                    const errorResult = {{
                        error: error.message,
                        errorStack: error.stack,
                        success: false
                    }};
                    window.customOrderApiResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''

            # 清空之前的响应
            tab.run_js('window.customOrderApiResponse = null;')
            self.log_message("🔄 执行JavaScript请求...", gui_only=True)
            self.log_console_detail("执行JavaScript代码...")

            # 执行JavaScript代码
            tab.run_js(js_code)

            # 等待响应
            max_wait_time = 15
            wait_interval = 0.5

            self.log_message(f"⏳ 等待API响应...", gui_only=True)
            self.log_console_detail(f"等待API响应 (最多{max_wait_time}秒)...")

            for i in range(int(max_wait_time / wait_interval)):
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.customOrderApiResponse;')
                    if response_data is not None:
                        self.log_message(f"✅ 获得API响应", gui_only=True)
                        self.log_console_detail(f"在第{i+1}次检查时获得响应")
                        break
                except Exception as e:
                    self.log_console_detail(f"第{i+1}次检查异常: {str(e)}")
            else:
                self.log_message("⏰ 等待响应超时")
                self.log_console_detail("等待响应超时")
                return None

            if response_data:
                if not response_data.get('success', True) or 'error' in response_data:
                    error_msg = response_data.get('error', '未知错误')
                    error_stack = response_data.get('errorStack', '无堆栈信息')
                    self.log_message(f"❌ JavaScript执行错误: {error_msg}")
                    self.log_console_detail(f"JavaScript执行错误详情:")
                    self.log_console_detail(f"  错误消息: {error_msg}")
                    self.log_console_detail(f"  错误堆栈: {error_stack}")
                    return None

                self.log_message("✅ API请求执行成功", gui_only=True)
                self.log_console_detail("API请求执行成功")
                return response_data
            else:
                self.log_message("❌ 未获取到响应数据")
                self.log_console_detail("未获取到响应数据")
                return None

        except Exception as e:
            self.log_message(f"❌ 发送API请求时出现异常: {str(e)}")
            self.log_console_detail(f"发送API请求异常详情: {str(e)}")
            import traceback
            self.log_console_detail(f"异常堆栈: {traceback.format_exc()}")
            return None

    def run(self):
        """运行GUI应用"""
        self.log_message("🎨 GUI界面已启动")
        self.log_message("📖 使用说明:")
        self.log_message("   1. 点击'开始监控'按钮启动API监控")
        self.log_message("   2. 系统会自动获取店铺列表")
        self.log_message("   3. 在店铺列表中选择要切换的店铺")
        self.log_message("   4. 点击'选择此店铺'确认切换")
        self.log_message("=" * 50)

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("👋 程序被用户中断")
        except Exception as e:
            self.log_message(f"❌ GUI运行时出现异常: {str(e)}")
        finally:
            if self.is_monitoring:
                self.stop_monitoring()
            self.log_message("👋 多店铺选取切换管理器已关闭")

if __name__ == "__main__":
    try:
        print("=" * 60)
        print("🚀 多店铺选取切换管理器")
        print("=" * 60)
        print("📋 功能特点:")
        print("   • 监控用户信息API获取店铺列表")
        print("   • 监控菜单权限API获取认证信息")
        print("   • 提供友好的GUI界面")
        print("   • 支持多店铺选择和切换")
        print("   • 详细的调试输出信息")
        print("=" * 60)

        # 创建并运行应用
        app = MultiStoreManager()
        app.run()

    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
